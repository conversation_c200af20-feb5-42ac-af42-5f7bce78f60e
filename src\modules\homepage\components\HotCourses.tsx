import React, { useState } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import { Course } from '@/types/data';

interface HotCoursesProps {
  categories: string[];
  courses: Course[];
  onCourseClick: (courseId: string) => void;
}

const HotCourses: React.FC<HotCoursesProps> = ({ 
  categories, 
  courses, 
  onCourseClick 
}) => {
  const [selectedCategory, setSelectedCategory] = useState('Tất cả');

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
  };

  return (
    <section className="w-full bg-global-11 py-8 sm:py-12 md:py-16">
      <div className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-6 sm:gap-8">
          <Button
            variant="primary"
            size="sm"
            className="bg-global-12 text-button-2 shadow-[0px_4px_30px_#eb725744]"
            leftIcon="/images/img_frame.svg"
          >
            KHÓA HỌC
          </Button>
          <h2 className="text-[20px] sm:text-[24px] md:text-[30px] font-bold text-global-4 text-center">
            Các khóa học Hot nhất 2025
          </h2>
          <div className="bg-global-10 rounded-[14px] p-2 flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => handleCategoryClick(category)}
                className={`px-3 py-1.5 rounded-[16px] text-sm font-bold transition-all duration-200 ${
                  selectedCategory === category
                    ? 'bg-button-1 text-global-11 shadow-[0px_1px_2px_#0000000c]'
                    : 'text-global-7 hover:bg-button-1 hover:text-global-11'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 w-full">
            {courses.slice(0, 8).map((course) => (
              <div
                key={course.id}
                className="bg-global-12 rounded-[24px] shadow-[0px_4px_6px_#00000019] overflow-hidden cursor-pointer hover:scale-105 transition-transform duration-200"
                onClick={() => onCourseClick(course.id)}
              >
                <Image
                  src={course.courseImage}
                  alt={course.title}
                  width={294}
                  height={204}
                  className="w-full h-[150px] sm:h-[180px] md:h-[204px] object-cover"
                />
                <div className="p-4 flex flex-col gap-3">
                  <h3 className="text-base sm:text-lg md:text-xl font-bold text-global-4 line-clamp-2 leading-tight">
                    {course.title}
                  </h3>
                  <div className="flex justify-end">
                    <span className="bg-global-4 text-global-11 text-sm font-bold px-3 py-1 rounded-[14px]">
                      {course.price}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Image
                        src={course.instructorImage}
                        alt={course.instructor}
                        width={32}
                        height={32}
                        className="w-8 h-8 rounded-full"
                      />
                      <span className="text-sm text-global-5 truncate">
                        {course.instructor}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-global-9">Chi tiết</span>
                      <Image
                        src="/images/img_arrow_right_yellow_900.svg"
                        alt="Arrow"
                        width={24}
                        height={24}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <Button
            variant="outline"
            size="md"
            className="border border-global-4 text-global-4 bg-global-12 hover:bg-global-4 hover:text-global-11"
          >
            Xem thêm
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HotCourses;
