import React from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';

const HealthSolutions: React.FC = () => {
  return (
    <section className="w-full max-w-[1440px] mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
      <div className="bg-global-12 rounded-[50px] p-6 sm:p-8 md:p-12 lg:p-[50px] shadow-lg">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
          <div className="w-full lg:w-[68%] flex flex-col gap-6 lg:gap-8">
            <div className="flex flex-col gap-2">
              <h1 className="text-[24px] sm:text-[30px] md:text-[35px] lg:text-[40px] font-bold leading-tight text-global-3">
                Giải Pháp Cho Sức Khỏe Bạn
              </h1>
              <p className="text-sm sm:text-base text-global-8 leading-relaxed">
                Chúng tôi hân hạnh cung cấp tới toàn thể quý cộng đồng những dòng giải pháp
                hữu ích giúp mọi người có thể chủ động nâng cao sức khỏe về cả thể chất lẫn
                tinh thần của mình
              </p>
            </div>
            <Button
              variant="primary"
              size="md"
              className="bg-global-3 text-global-11 rounded-[14px] px-4 py-2 w-fit"
              rightIcon="/images/img_arrow_right.svg"
            >
              XEM TẤT CẢ
            </Button>
          </div>
          <div className="w-full lg:w-[32%] flex flex-col gap-4">
            <div className="flex gap-4">
              <Image
                src="/images/img_image_192x292.png"
                alt="Y Học Cổ Truyền"
                width={292}
                height={192}
                className="w-1/2 rounded-[14px]"
              />
              <Image
                src="/images/img_image_1.png"
                alt="Giáo Dục Đặc Biệt"
                width={292}
                height={192}
                className="w-1/2 rounded-[14px]"
              />
            </div>
            <div className="flex justify-between text-center">
              <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                Y Học Cổ Truyền
              </h3>
              <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
                Giáo Dục Đặc Biệt
              </h3>
            </div>
          </div>
        </div>
        <div className="mt-8 lg:mt-12">
          <div className="flex flex-wrap gap-4 justify-center">
            <Image
              src="/images/img_image_192x296.png"
              alt="Dinh Dưỡng"
              width={296}
              height={192}
              className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
            />
            <Image
              src="/images/img_image_2.png"
              alt="Cắm Hoa"
              width={296}
              height={192}
              className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
            />
            <Image
              src="/images/img_image_3.png"
              alt="Yoga"
              width={296}
              height={192}
              className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
            />
            <Image
              src="/images/img_image_4.png"
              alt="Âm Nhạc"
              width={296}
              height={192}
              className="w-full sm:w-[48%] md:w-[23%] rounded-[14px]"
            />
          </div>
          <div className="flex flex-wrap justify-between mt-4 text-center">
            <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
              Dinh Dưỡng
            </h3>
            <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
              Cắm Hoa
            </h3>
            <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
              Yoga
            </h3>
            <h3 className="w-full sm:w-[48%] md:w-[23%] text-lg sm:text-xl md:text-2xl font-semibold text-global-3">
              Âm Nhạc
            </h3>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HealthSolutions;
