'use client';
import React from 'react';
import AppHeader from '@/components/common/AppHeader';
import { useHomePageData } from '@/hooks/useHomePageData';
import {
  LoadingState,
  ErrorState,
  HeroSlider,
  HealthSolutions,
  Features,
  HotCourses,
  AboutOme,
  Instructors,
  Articles,
  Testimonials,
  Partners,
  CtaSection,
} from '@/modules/homepage/components';
import AppFooter from '@/components/common/AppFooter';
const HomePage: React.FC = () => {
  // Load data using custom hook
  const { data, isLoading, error, refetch } = useHomePageData();
  const { categories, courses, instructors, articles, testimonials, partners } = data;

  const handleCourseClick = (courseId: string) => {
    console.log('Course clicked:', courseId);
  };
  const handleInstructorClick = (instructorId: string) => {
    console.log('Instructor clicked:', instructorId);
  };
  const handleArticleClick = (articleId: string) => {
    console.log('Article clicked:', articleId);
  };

  // Show loading state
  if (isLoading) {
    return <LoadingState />;
  }

  // Show error state
  if (error) {
    return <ErrorState error={error} onRetry={refetch} />;
  }

  return (
    <>
      <AppHeader />
      <div className="w-full bg-global-12 overflow-x-hidden">
        <HeroSlider />
        {/* Main Content */}
        <div className="w-full flex flex-col items-center">
          <HealthSolutions />
          <Features />
          <HotCourses
            categories={categories}
            courses={courses}
            onCourseClick={handleCourseClick}
          />
          <AboutOme />
          <Instructors
            instructors={instructors}
            onInstructorClick={handleInstructorClick}
          />
          <Articles
            articles={articles}
            onArticleClick={handleArticleClick}
          />
          <Testimonials testimonials={testimonials} />
          <Partners partners={partners} />
          <CtaSection />
        </div>
      </div>

      <AppFooter />
    </>
  );
};
export default HomePage;
